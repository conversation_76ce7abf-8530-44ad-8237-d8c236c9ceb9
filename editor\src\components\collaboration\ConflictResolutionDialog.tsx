/**
 * 冲突解决对话框组件
 */
import React, { useState } from 'react';
import {
  Modal,
  Button,
  Radio,
  Space,
  Divider,
  Typography,
  Tabs,
  Card,
  Row,
  Col,
  Alert,
  Table,
  Tag,
  Form,
  Input
} from 'antd';
import {
  MergeCellsOutlined,
  UserOutlined,
  CloudOutlined,
  CheckCircleOutlined,
  WarningOutlined,
  ExclamationCircleOutlined,
  EditOutlined,
  RobotOutlined,
  BulbOutlined,
  LikeOutlined,
  DislikeOutlined
} from '@ant-design/icons';

import {
  Conflict,
  ConflictType,
  ConflictResolutionStrategy,
  conflictResolutionService
} from '../../services/ConflictResolutionService';
import { Operation, OperationType } from '../../services/CollaborationService';
import JsonView from '../common/JsonView';

const { Title, Text } = Typography;
const { TabPane } = Tabs;

// 组件属性
interface ConflictResolutionDialogProps {
  visible: boolean;
  conflict: Conflict;
  onClose: () => void;
}

// 获取操作类型名称
const getOperationTypeName = (type: OperationType): string => {
  switch (type) {
    case OperationType.ENTITY_CREATE:
      return '创建实体';
    case OperationType.ENTITY_UPDATE:
      return '更新实体';
    case OperationType.ENTITY_DELETE:
      return '删除实体';
    case OperationType.COMPONENT_ADD:
      return '添加组件';
    case OperationType.COMPONENT_UPDATE:
      return '更新组件';
    case OperationType.COMPONENT_REMOVE:
      return '移除组件';
    case OperationType.SCENE_UPDATE:
      return '更新场景';
    case OperationType.CURSOR_MOVE:
      return '移动光标';
    case OperationType.SELECTION_CHANGE:
      return '改变选择';
    default:
      return '未知操作';
  }
};

// 获取操作描述
const getOperationDescription = (operation: Operation): string => {
  switch (operation.type) {
    case OperationType.ENTITY_CREATE:
      return `创建实体 "${operation.data.name || operation.data.id}"`;

    case OperationType.ENTITY_UPDATE:
      return `更新实体 "${operation.data.name || operation.data.id}"`;

    case OperationType.ENTITY_DELETE:
      return `删除实体 "${operation.data.name || operation.data.id}"`;

    case OperationType.COMPONENT_ADD:
      return `向实体 "${operation.data.entityName || operation.data.entityId}" 添加组件 "${operation.data.componentType}"`;

    case OperationType.COMPONENT_UPDATE:
      return `更新实体 "${operation.data.entityName || operation.data.entityId}" 的组件 "${operation.data.componentType}"`;

    case OperationType.COMPONENT_REMOVE:
      return `从实体 "${operation.data.entityName || operation.data.entityId}" 移除组件 "${operation.data.componentType}"`;

    case OperationType.SCENE_UPDATE:
      return `更新场景属性`;

    case OperationType.CURSOR_MOVE:
      return `移动光标到 (${operation.data.x}, ${operation.data.y})`;

    case OperationType.SELECTION_CHANGE:
      const count = operation.data.selectedIds?.length || 0;
      return `选择了 ${count} 个实体`;

    default:
      return `未知操作`;
  }
};

/**
 * 冲突解决对话框组件
 */
const ConflictResolutionDialog: React.FC<ConflictResolutionDialogProps> = ({
  visible,
  conflict,
  onClose
}) => {

  // 状态
  const [resolutionStrategy, setResolutionStrategy] = useState<ConflictResolutionStrategy>(
    ConflictResolutionStrategy.MERGE
  );
  const [customResolution, setCustomResolution] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [aiSuggestions, setAiSuggestions] = useState<{
    strategy: ConflictResolutionStrategy;
    confidence: number;
    explanation: string;
    mergedData?: any;
  }[]>([]);
  const [aiLoading, setAiLoading] = useState(false);
  const [showAiSuggestions, setShowAiSuggestions] = useState(false);
  const [feedbackSubmitted, setFeedbackSubmitted] = useState(false);

  // 处理解决策略变更
  const handleStrategyChange = (e: any) => {
    setResolutionStrategy(e.target.value);
  };

  // 处理确认
  const handleConfirm = () => {
    setLoading(true);

    // 解决冲突
    conflictResolutionService.resolveConflict(
      conflict.id,
      resolutionStrategy,
      customResolution
    );

    setLoading(false);
    onClose();
  };

  // 处理取消
  const handleCancel = () => {
    onClose();
  };

  // 加载AI建议
  const loadAiSuggestions = async () => {
    setAiLoading(true);
    setShowAiSuggestions(true);

    try {
      const suggestions = await conflictResolutionService.getAISuggestions(conflict.id);
      setAiSuggestions(suggestions);
    } catch (error) {
      console.error('获取AI建议失败:', error);
    } finally {
      setAiLoading(false);
    }
  };

  // 应用AI建议
  const applyAiSuggestion = (suggestion: {
    strategy: ConflictResolutionStrategy;
    confidence: number;
    explanation: string;
    mergedData?: any;
  }) => {
    setResolutionStrategy(suggestion.strategy);
    if (suggestion.mergedData) {
      setCustomResolution(suggestion.mergedData);
    }
  };

  // 提交AI反馈
  const submitAiFeedback = (wasHelpful: boolean, comment?: string) => {
    conflictResolutionService.provideAIFeedback(
      conflict.id,
      wasHelpful,
      resolutionStrategy,
      comment
    );
    setFeedbackSubmitted(true);
  };

  // 渲染操作数据
  const renderOperationData = (operation: Operation) => {
    return (
      <Card className="operation-data-card">
        <Title level={5}>{getOperationTypeName(operation.type)}</Title>
        <Paragraph>{getOperationDescription(operation)}</Paragraph>
        <Divider />
        <JsonView data={operation.data} />
      </Card>
    );
  };

  // 渲染合并预览
  const renderMergePreview = () => {
    // 根据冲突类型生成合并预览
    let mergePreview: any = null;

    switch (conflict.type) {
      case ConflictType.ENTITY_CONFLICT:
      case ConflictType.COMPONENT_CONFLICT:
        mergePreview = renderPropertyMergePreview();
        break;

      case ConflictType.PROPERTY_CONFLICT:
        mergePreview = renderPropertyMergePreview();
        break;

      case ConflictType.SCENE_CONFLICT:
        mergePreview = renderSceneMergePreview();
        break;

      case ConflictType.DELETION_CONFLICT:
        mergePreview = renderDeletionConflictPreview();
        break;

      default:
        mergePreview = (
          <Alert
            message="无法预览"
            description="此类型的冲突无法自动合并，请选择其他解决方案。"
            type="warning"
            showIcon
          />
        );
        break;
    }

    return (
      <div className="merge-preview">
        <Alert
          message="合并预览"
          description="系统将尝试合并两个版本的更改。"
          type="info"
          showIcon
        />

        <div style={{ marginTop: 16 }}>
          {mergePreview}
        </div>
      </div>
    );
  };

  // 渲染属性合并预览
  const renderPropertyMergePreview = () => {
    const localProps = conflict.localOperation.data?.properties || {};
    const remoteProps = conflict.remoteOperation.data?.properties || {};

    // 合并属性
    const mergedProps = { ...remoteProps, ...localProps };

    // 找出冲突的属性（两边都修改了的属性）
    const conflictProps: string[] = [];
    for (const key in localProps) {
      if (key in remoteProps && JSON.stringify(localProps[key]) !== JSON.stringify(remoteProps[key])) {
        conflictProps.push(key);
      }
    }

    return (
      <div>
        <Table
          dataSource={Object.keys(mergedProps).map(key => ({
            key,
            property: key,
            localValue: JSON.stringify(localProps[key] !== undefined ? localProps[key] : '未修改'),
            remoteValue: JSON.stringify(remoteProps[key] !== undefined ? remoteProps[key] : '未修改'),
            mergedValue: JSON.stringify(mergedProps[key]),
            conflict: conflictProps.includes(key)
          }))}
          columns={[
            {
              title: '属性',
              dataIndex: 'property',
              key: 'property',
              render: (text, record: any) => (
                <span>
                  {text}
                  {record.conflict && (
                    <Tag color="red" style={{ marginLeft: 8 }}>冲突</Tag>
                  )}
                </span>
              )
            },
            {
              title: '本地值',
              dataIndex: 'localValue',
              key: 'localValue'},
            {
              title: '远程值',
              dataIndex: 'remoteValue',
              key: 'remoteValue'},
            {
              title: '合并结果',
              dataIndex: 'mergedValue',
              key: 'mergedValue',
              render: (text, record: any) => (
                <span style={{ color: record.conflict ? 'red' : 'green' }}>
                  {text}
                </span>
              )
            }
          ]}
          size="small"
          pagination={false}
        />

        {conflictProps.length > 0 && (
          <Alert
            message="警告"
            description="存在无法自动合并的属性，合并后将使用本地值覆盖远程值。"
            type="warning"
            showIcon
            style={{ marginTop: 16 }}
          />
        )}
      </div>
    );
  };

  // 渲染场景合并预览
  const renderSceneMergePreview = () => {
    const localProps = conflict.localOperation.data?.properties || {};
    const remoteProps = conflict.remoteOperation.data?.properties || {};

    // 合并属性
    const mergedProps = { ...remoteProps, ...localProps };

    // 找出冲突的属性（两边都修改了的属性）
    const conflictProps: string[] = [];
    for (const key in localProps) {
      if (key in remoteProps && JSON.stringify(localProps[key]) !== JSON.stringify(remoteProps[key])) {
        conflictProps.push(key);
      }
    }

    return (
      <div>
        <Table
          dataSource={Object.keys(mergedProps).map(key => ({
            key,
            property: key,
            localValue: JSON.stringify(localProps[key] !== undefined ? localProps[key] : '未修改'),
            remoteValue: JSON.stringify(remoteProps[key] !== undefined ? remoteProps[key] : '未修改'),
            mergedValue: JSON.stringify(mergedProps[key]),
            conflict: conflictProps.includes(key)
          }))}
          columns={[
            {
              title: '场景属性',
              dataIndex: 'property',
              key: 'property',
              render: (text, record: any) => (
                <span>
                  {text}
                  {record.conflict && (
                    <Tag color="red" style={{ marginLeft: 8 }}>冲突</Tag>
                  )}
                </span>
              )
            },
            {
              title: '本地值',
              dataIndex: 'localValue',
              key: 'localValue'},
            {
              title: '远程值',
              dataIndex: 'remoteValue',
              key: 'remoteValue'},
            {
              title: '合并结果',
              dataIndex: 'mergedValue',
              key: 'mergedValue',
              render: (text, record: any) => (
                <span style={{ color: record.conflict ? 'red' : 'green' }}>
                  {text}
                </span>
              )
            }
          ]}
          size="small"
          pagination={false}
        />

        {conflictProps.length > 0 && (
          <Alert
            message="警告"
            description="存在无法自动合并的场景属性，合并后将使用本地值覆盖远程值。"
            type="warning"
            showIcon
            style={{ marginTop: 16 }}
          />
        )}
      </div>
    );
  };

  // 渲染删除冲突预览
  const renderDeletionConflictPreview = () => {
    const isLocalDelete = conflict.localOperation.type === OperationType.ENTITY_DELETE;
    const isRemoteDelete = conflict.remoteOperation.type === OperationType.ENTITY_DELETE;

    if (isLocalDelete && isRemoteDelete) {
      return (
        <Alert
          message="双方都删除了同一实体"
          description="两个操作都是删除操作，可以安全合并。"
          type="success"
          showIcon
        />
      );
    }

    if (isLocalDelete) {
      return (
        <Alert
          message="本地删除冲突"
          description="本地操作删除了实体，而远程操作尝试修改该实体。合并后将保留删除操作，远程修改将被忽略。"
          type="warning"
          showIcon
        />
      );
    }

    if (isRemoteDelete) {
      return (
        <Alert
          message="远程删除冲突"
          description="远程操作删除了实体，而本地操作尝试修改该实体。合并后将保留删除操作，本地修改将被忽略。"
          type="warning"
          showIcon
        />
      );
    }

    return (
      <Alert
        message="未知删除冲突"
        description="无法确定删除冲突的类型，请手动选择解决方案。"
        type="error"
        showIcon
      />
    );
  };

  // 获取冲突类型名称
  const getConflictTypeName = (type: ConflictType): string => {
    switch (type) {
      case ConflictType.ENTITY_CONFLICT:
        return '实体冲突';
      case ConflictType.COMPONENT_CONFLICT:
        return '组件冲突';
      case ConflictType.PROPERTY_CONFLICT:
        return '属性冲突';
      case ConflictType.DELETION_CONFLICT:
        return '删除冲突';
      case ConflictType.SCENE_CONFLICT:
        return '场景冲突';
      default:
        return '未知冲突';
    }
  };

  // 获取冲突原因
  const getConflictReason = (conflict: Conflict): string => {
    const { localOperation, remoteOperation, type } = conflict;

    switch (type) {
      case ConflictType.ENTITY_CONFLICT:
        return '多个用户同时修改了同一个实体';

      case ConflictType.COMPONENT_CONFLICT:
        return '多个用户同时修改了同一个组件';

      case ConflictType.PROPERTY_CONFLICT:
        return '多个用户同时修改了同一个属性';

      case ConflictType.DELETION_CONFLICT:
        return '一个用户尝试修改另一个用户已删除的实体';

      case ConflictType.SCENE_CONFLICT:
        return '多个用户同时修改了场景属性';

      default:
        return '未知冲突原因';
    }
  };

  // 获取建议的解决方案
  const getSuggestedResolution = (conflict: Conflict): string => {
    const { localOperation, remoteOperation, type } = conflict;

    switch (type) {
      case ConflictType.ENTITY_CONFLICT:
      case ConflictType.COMPONENT_CONFLICT:
        // 检查是否修改了不同的属性
        const localProps = Object.keys(localOperation.data?.properties || {});
        const remoteProps = Object.keys(remoteOperation.data?.properties || {});
        const hasIntersection = localProps.some(prop => remoteProps.includes(prop));

        if (!hasIntersection) {
          return '可以安全合并，因为修改了不同的属性';
        }
        return '建议手动检查并选择合适的版本';

      case ConflictType.PROPERTY_CONFLICT:
        return '建议选择最新的修改';

      case ConflictType.DELETION_CONFLICT:
        return '建议接受删除操作，或者创建新实体';

      case ConflictType.SCENE_CONFLICT:
        return '建议手动检查并选择合适的版本';

      default:
        return '请手动解决此冲突';
    }
  };

  // 渲染AI建议
  const renderAiSuggestions = () => {
    if (!showAiSuggestions) {
      return (
        <Button
          type="primary"
          icon={<RobotOutlined />}
          onClick={loadAiSuggestions}
          loading={aiLoading}
        >
          获取AI建议
        </Button>
      );
    }

    if (aiLoading) {
      return (
        <div style={{ textAlign: 'center', padding: '20px' }}>
          <div>AI正在分析冲突...</div>
        </div>
      );
    }

    if (aiSuggestions.length === 0) {
      return (
        <Alert
          message="无法获取AI建议"
          description="AI无法为此冲突提供建议，请手动解决。"
          type="warning"
          showIcon
        />
      );
    }

    return (
      <div>
        <div style={{ marginBottom: 16 }}>
          <Title level={5}>
            <RobotOutlined /> AI建议
          </Title>
          <Text>AI已分析此冲突并提供以下解决建议：</Text>
        </div>

        {aiSuggestions.map((suggestion, index) => (
          <Card
            key={index}
            style={{ marginBottom: 8 }}
            size="small"
            title={
              <Space>
                <BulbOutlined />
                <span>建议 {index + 1}</span>
                <Tag color={suggestion.confidence > 0.8 ? 'green' : suggestion.confidence > 0.5 ? 'blue' : 'orange'}>
                  置信度: {Math.round(suggestion.confidence * 100)}%
                </Tag>
              </Space>
            }
            extra={
              <Button
                type="primary"
                size="small"
                onClick={() => applyAiSuggestion(suggestion)}
              >
                应用
              </Button>
            }
          >
            <div>
              <div><strong>策略:</strong> {getStrategyName(suggestion.strategy)}</div>
              <div><strong>说明:</strong> {suggestion.explanation}</div>
            </div>
          </Card>
        ))}

        {!feedbackSubmitted && (
          <div style={{ marginTop: 16 }}>
            <Divider>反馈</Divider>
            <Text>这些AI建议对您有帮助吗？</Text>
            <div style={{ marginTop: 8 }}>
              <Space>
                <Button
                  icon={<LikeOutlined />}
                  onClick={() => submitAiFeedback(true)}
                >
                  有帮助
                </Button>
                <Button
                  icon={<DislikeOutlined />}
                  onClick={() => submitAiFeedback(false)}
                >
                  没帮助
                </Button>
              </Space>
            </div>
          </div>
        )}
      </div>
    );
  };

  // 获取策略名称
  const getStrategyName = (strategy: ConflictResolutionStrategy): string => {
    switch (strategy) {
      case ConflictResolutionStrategy.ACCEPT_LOCAL:
        return '采用本地版本';
      case ConflictResolutionStrategy.ACCEPT_REMOTE:
        return '采用远程版本';
      case ConflictResolutionStrategy.MERGE:
        return '合并两个版本';
      case ConflictResolutionStrategy.CUSTOM:
        return '自定义解决方案';
      default:
        return '未知策略';
    }
  };

  return (
    <Modal
      title={
        <Space>
          <MergeCellsOutlined />
          <span>解决冲突</span>
        </Space>
      }
      open={visible}
      width={800}
      onCancel={handleCancel}
      footer={[
        <Button key="cancel" onClick={handleCancel}>
          取消
        </Button>,
        <Button
          key="confirm"
          type="primary"
          loading={loading}
          onClick={handleConfirm}
        >
          确认解决
        </Button>
      ]}
    >
      <div className="conflict-resolution-dialog">
        <Alert
          message="检测到编辑冲突"
          description="您和其他用户同时编辑了相同的内容，请选择如何解决此冲突。"
          type="warning"
          showIcon
          style={{ marginBottom: 16 }}
        />

        <Tabs defaultActiveKey="compare">
          <TabPane
            tab={
              <span>
                <MergeCellsOutlined />
                比较差异
              </span>
            }
            key="compare"
          >
            <Row gutter={16}>
              <Col span={12}>
                <Card
                  title={
                    <Space>
                      <UserOutlined />
                      <span>本地版本</span>
                    </Space>
                  }
                  className="version-card"
                  extra={
                    <Button
                      type="link"
                      size="small"
                      onClick={() => setResolutionStrategy(ConflictResolutionStrategy.ACCEPT_LOCAL)}
                    >
                      采用此版本
                    </Button>
                  }
                >
                  {renderOperationData(conflict.localOperation)}
                </Card>
              </Col>
              <Col span={12}>
                <Card
                  title={
                    <Space>
                      <CloudOutlined />
                      <span>远程版本</span>
                    </Space>
                  }
                  className="version-card"
                  extra={
                    <Button
                      type="link"
                      size="small"
                      onClick={() => setResolutionStrategy(ConflictResolutionStrategy.ACCEPT_REMOTE)}
                    >
                      采用此版本
                    </Button>
                  }
                >
                  {renderOperationData(conflict.remoteOperation)}
                </Card>
              </Col>
            </Row>

            <div style={{ marginTop: 16 }}>
              <Card
                title={
                  <Space>
                    <ExclamationCircleOutlined />
                    <span>冲突分析</span>
                  </Space>
                }
                size="small"
              >
                <div>
                  <Text strong>冲突类型:</Text> {getConflictTypeName(conflict.type)}
                </div>
                <div>
                  <Text strong>冲突原因:</Text> {getConflictReason(conflict)}
                </div>
                <div>
                  <Text strong>建议解决方案:</Text> {getSuggestedResolution(conflict)}
                </div>
              </Card>
            </div>
          </TabPane>

          <TabPane
            tab={
              <span>
                <RobotOutlined />
                AI建议
              </span>
            }
            key="ai"
          >
            <div style={{ padding: '16px' }}>
              {renderAiSuggestions()}
            </div>
          </TabPane>

          <TabPane
            tab={
              <span>
                <CheckCircleOutlined />
                解决方案
              </span>
            }
            key="resolution"
          >
            <div className="resolution-options">
              <Title level={5}>选择解决策略</Title>

              <Radio.Group
                value={resolutionStrategy}
                onChange={handleStrategyChange}
              >
                <Space direction="vertical">
                  <Radio value={ConflictResolutionStrategy.ACCEPT_LOCAL}>
                    <Space>
                      <UserOutlined />
                      <span>采用本地版本</span>
                    </Space>
                    <div className="option-description">
                      保留您的更改，忽略远程更改。
                    </div>
                  </Radio>

                  <Radio value={ConflictResolutionStrategy.ACCEPT_REMOTE}>
                    <Space>
                      <CloudOutlined />
                      <span>采用远程版本</span>
                    </Space>
                    <div className="option-description">
                      放弃您的更改，采用远程更改。
                    </div>
                  </Radio>

                  <Radio value={ConflictResolutionStrategy.MERGE}>
                    <Space>
                      <MergeCellsOutlined />
                      <span>合并版本</span>
                    </Space>
                    <div className="option-description">
                      尝试自动合并两个版本的更改。
                    </div>
                  </Radio>

                  <Radio value={ConflictResolutionStrategy.CUSTOM}>
                    <Space>
                      <EditOutlined />
                      <span>自定义解决</span>
                    </Space>
                    <div className="option-description">
                      手动编辑解决方案。
                    </div>
                  </Radio>
                </Space>
              </Radio.Group>

              {resolutionStrategy === ConflictResolutionStrategy.MERGE && (
                <div className="merge-preview" style={{ marginTop: 16 }}>
                  {renderMergePreview()}
                </div>
              )}

              {resolutionStrategy === ConflictResolutionStrategy.CUSTOM && (
                <div className="custom-resolution" style={{ marginTop: 16 }}>
                  <Title level={5}>自定义解决方案</Title>
                  <Alert
                    message="提示"
                    description="您可以手动编辑数据来解决冲突。"
                    type="info"
                    showIcon
                    style={{ marginBottom: 16 }}
                  />

                  <Form layout="vertical">
                    <Form.Item label="自定义数据">
                      <Input.TextArea
                        rows={6}
                        defaultValue={JSON.stringify(conflict.localOperation.data, null, 2)}
                        onChange={(e) => {
                          try {
                            setCustomResolution(JSON.parse(e.target.value));
                          } catch (error) {
                            console.error('JSON解析错误:', error);
                          }
                        }}
                      />
                    </Form.Item>
                  </Form>
                </div>
              )}
            </div>
          </TabPane>
        </Tabs>
      </div>
    </Modal>
  );
};

export default ConflictResolutionDialog;
